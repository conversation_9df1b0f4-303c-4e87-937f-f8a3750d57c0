import AlignHorizontalCenterIcon from "@mui/icons-material/AlignHorizontalCenter";
import AlignHorizontalLeftIcon from "@mui/icons-material/AlignHorizontalLeft";
import AlignHorizontalRightIcon from "@mui/icons-material/AlignHorizontalRight";
import AlignVerticalBottomIcon from "@mui/icons-material/AlignVerticalBottom";
import AlignVerticalCenterIcon from "@mui/icons-material/AlignVerticalCenter";
import AlignVerticalTopIcon from "@mui/icons-material/AlignVerticalTop";
import DeleteIcon from "@mui/icons-material/Delete";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import LockIcon from "@mui/icons-material/Lock";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import Opacity from "@mui/icons-material/Opacity";
import {
  Box,
  IconButton,
  Popover,
  Slider,
  Stack,
  Tooltip,
  Typo<PERSON>,
  <PERSON>Field,
  Grid,
} from "@mui/material";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import FlipIcon from "@mui/icons-material/Flip";
import { EditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";

interface BaseSettingProps {
  element: EditorElement | null;
}

const BaseSetting = observer(({ element }: BaseSettingProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [anchorEl, setAnchorEl] = React.useState(null);

  if (!element) {
    return (
      <Box sx={{ height: "100%", p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t("no_element_selected")}
        </Typography>
      </Box>
    );
  }

  const handleAlign = (alignType) => {
    store.alignElement(element.id, alignType);
  };

  const handleLock = () => {
    store.toggleLockElement(element.id);
  };

  const handleClone = () => {
    store.cloneElement(element.id);
  };

  const handleDelete = () => {
    store.deleteElement(element.id);
  };

  const handleFullscreen = () => {
    store.setElementFullscreen(element.id);
  };

  const handleOpacityClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleOpacityClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const handleFlip = (flipType: "horizontal" | "vertical") => {
    store.flipElement(element.id, flipType);
  };

  const handleLayoutChange = (property: string, value: number) => {
    if (element.fabricObject) {
      // 创建更新后的placement对象
      const updatedPlacement = { ...element.placement };
      updatedPlacement[property] = value;

      // 创建更新后的元素对象
      const updatedElement = {
        ...element,
        placement: updatedPlacement,
      };

      if (property === "x" || property === "y") {
        element.fabricObject.set(property === "x" ? "left" : "top", value);
      } else if (property === "width" || property === "height") {
        console.log(`设置面板修改${property}:`, value);
        // 对于文字元素，需要特殊处理尺寸变化
        if (element.type === "text") {
          // 重置缩放因子，直接设置尺寸
          element.fabricObject.set({
            [property]: value,
            scaleX: 1,
            scaleY: 1,
          });

          // 如果是文字元素且改变了尺寸，可能需要调整字体大小
          // 这里可以根据需要添加字体大小的自动调整逻辑
        } else {
          element.fabricObject.set(property, value);
        }
      } else if (property === "rotation") {
        element.fabricObject.set("angle", value);
      }

      // 通过store更新，确保所有组件都能收到更新
      store.updateEditorElement(updatedElement, "元素属性修改");

      store.canvas.requestRenderAll();
    }
  };

  return (
    <Box sx={{ height: "100%" }}>
      <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
        <Tooltip title={element.locked ? t("unlock") : t("lock")}>
          <IconButton onClick={handleLock}>
            {element.locked ? (
              <LockIcon fontSize="small" />
            ) : (
              <LockOpenIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title={t("opacity")}>
          <IconButton onClick={handleOpacityClick}>
            <Opacity fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={t("clone")}>
          <IconButton onClick={handleClone}>
            <FileCopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={t("delete")}>
          <IconButton onClick={handleDelete}>
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={t("fullscreen")}>
          <IconButton onClick={handleFullscreen}>
            <FullscreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={t("flip_horizontal")}>
          <IconButton onClick={() => handleFlip("horizontal")}>
            <FlipIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={t("flip_vertical")}>
          <IconButton onClick={() => handleFlip("vertical")}>
            <FlipIcon fontSize="small" sx={{ transform: "rotate(90deg)" }} />
          </IconButton>
        </Tooltip>
      </Stack>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleOpacityClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box sx={{ p: 2, width: 250 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Slider
              value={element.opacity * 100}
              onChange={(_, newValue) => {
                store.updateElementOpacity(
                  element.id,
                  (newValue as number) / 100
                );
              }}
              aria-labelledby="opacity-slider"
              valueLabelDisplay="off"
              step={1}
              min={0}
              max={100}
              sx={{
                color: "#1976d2",
                "& .MuiSlider-thumb": {
                  width: 12,
                  height: 12,
                  transition: "0.3s cubic-bezier(.47,1.64,.41,.8)",
                  "&:before": {
                    boxShadow: "0 2px 12px 0 rgba(0,0,0,0.4)",
                  },
                  "&:hover, &.Mui-focusVisible": {
                    boxShadow: `0px 0px 0px 8px rgb(25 118 210 / 16%)`,
                  },
                },
                "& .MuiSlider-rail": {
                  opacity: 0.5,
                },
              }}
            />
            <Typography
              variant="body2"
              sx={{ minWidth: 35, textAlign: "right", color: "text.secondary" }}
            >
              {Math.round(element.opacity * 100)}%
            </Typography>
          </Stack>
        </Box>
      </Popover>
      <Typography variant="subtitle2" sx={{ mb: 1, color: "text.secondary" }}>
        {t("alignment")}
      </Typography>

      <Stack direction="row">
        {[
          {
            icon: AlignHorizontalLeftIcon,
            align: "left",
            tooltip: t("align_left"),
          },
          {
            icon: AlignHorizontalCenterIcon,
            align: "center",
            tooltip: t("align_center"),
          },
          {
            icon: AlignHorizontalRightIcon,
            align: "right",
            tooltip: t("align_right"),
          },
          {
            icon: FormatAlignJustifyIcon,
            align: "justify",
            tooltip: t("justify"),
          },
          { icon: AlignVerticalTopIcon, align: "top", tooltip: t("align_top") },
          {
            icon: AlignVerticalCenterIcon,
            align: "middle",
            tooltip: t("align_middle"),
          },
          {
            icon: AlignVerticalBottomIcon,
            align: "bottom",
            tooltip: t("align_bottom"),
          },
        ].map(({ icon: Icon, align, tooltip }, index) => (
          <Tooltip key={index} title={tooltip}>
            <IconButton onClick={() => handleAlign(align)}>
              <Icon fontSize="small" />
            </IconButton>
          </Tooltip>
        ))}
      </Stack>

      <Typography variant="subtitle2" sx={{ my: 1, color: "text.secondary" }}>
        {t("position")}
      </Typography>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          <TextField
            label={t("position_x")}
            type="number"
            value={Math.round(element.placement?.x)}
            onChange={(e) => handleLayoutChange("x", Number(e.target.value))}
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label={t("position_y")}
            type="number"
            value={Math.round(element.placement?.y)}
            onChange={(e) => handleLayoutChange("y", Number(e.target.value))}
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label={t("width")}
            type="number"
            value={Math.round(element.placement?.width)}
            onChange={(e) =>
              handleLayoutChange("width", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label={t("height")}
            type="number"
            value={Math.round(element.placement?.height)}
            onChange={(e) =>
              handleLayoutChange("height", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            label={t("rotation")}
            type="number"
            value={Math.round(element.placement?.rotation)}
            onChange={(e) =>
              handleLayoutChange("rotation", Number(e.target.value))
            }
            fullWidth
            size="small"
          />
        </Grid>
      </Grid>
    </Box>
  );
});

export default BaseSetting;
